// === RedFox Portfolio Interactivity ===

// Constants
const ANIMATION_THRESHOLD = 0.15;
const SKILL_ANIMATION_THRESHOLD = 0.85;
const ANIMATION_DURATION = 1000;
const ANIMATION_EASING = "cubic-bezier(0.4,1.4,0.6,1)";
const GRADIENT_COLORS = {
  start: "#FF7F2A",
  end: "#FF9900",
};

// Utility Functions
const createElement = (tag, className, attributes = {}) => {
  const element = document.createElement(tag);
  if (className) element.className = className;
  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });
  return element;
};

// Navigation Module
const initNavigation = () => {
  document.querySelectorAll(".navbar a").forEach((link) => {
    link.addEventListener("click", function (e) {
      const targetId = this.getAttribute("href").slice(1);
      const target = document.getElementById(targetId);
      if (target) {
        e.preventDefault();
        target.scrollIntoView({ behavior: "smooth" });
      }
    });
  });
};

// Section Animation Module
const initSectionAnimations = () => {
  const sectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("visible");
        }
      });
    },
    { threshold: ANIMATION_THRESHOLD }
  );

  document.querySelectorAll(".section").forEach((section) => {
    sectionObserver.observe(section);
  });
};

// Skills Animation Module
const initSkillsAnimation = () => {
  const skillSection = document.getElementById("competences");
  let skillsAnimated = false;

  const animateSkillBar = (bar) => {
    const level = bar.getAttribute("data-level");
    const inner = createElement("div", "bar-inner");

    Object.assign(inner.style, {
      position: "absolute",
      left: "0",
      top: "0",
      height: "100%",
      width: "0%",
      background: `linear-gradient(90deg, ${GRADIENT_COLORS.start}, ${GRADIENT_COLORS.end})`,
      boxShadow: `0 0 8px ${GRADIENT_COLORS.end}`,
      borderRadius: "4px",
      transition: "width 1.2s cubic-bezier(0.4, 1.4, 0.6, 1)",
    });

    bar.innerHTML = "";
    bar.appendChild(inner);

    setTimeout(() => {
      inner.style.width = `${level}%`;
    }, 200);
  };

  const checkAndAnimateSkills = () => {
    if (
      !skillsAnimated &&
      skillSection.getBoundingClientRect().top <
        window.innerHeight * SKILL_ANIMATION_THRESHOLD
    ) {
      document.querySelectorAll(".skill-bar").forEach(animateSkillBar);
      skillsAnimated = true;
    }
  };

  window.addEventListener("scroll", checkAndAnimateSkills);
  window.addEventListener("load", checkAndAnimateSkills);
};

// Contact Form Module
const initContactForm = () => {
  const contactForm = document.querySelector(".contact-form");
  const foxMascotContact = document.getElementById("fox-mascot-contact");

  if (!contactForm || !foxMascotContact) return;

  contactForm.addEventListener("submit", function (e) {
    e.preventDefault();

    foxMascotContact.animate(
      [
        { transform: "translateY(0)" },
        { transform: "translateY(-18px)" },
        { transform: "translateY(0)" },
      ],
      {
        duration: ANIMATION_DURATION,
        easing: ANIMATION_EASING,
      }
    );

    const btn = contactForm.querySelector('button[type="submit"]');
    btn.textContent = "Envoyé !";
    setTimeout(() => {
      btn.textContent = "Envoyer";
      contactForm.reset();
    }, 1800);
  });
};

// Mascot Interaction Module
const initMascotInteraction = () => {
  const foxMascot = document.getElementById("fox-mascot");
  const accueilTitle = document.querySelector(".accueil-content h1");

  if (!foxMascot || !accueilTitle) return;

  accueilTitle.addEventListener("mouseenter", () => {
    foxMascot.style.opacity = "0.5";
    setTimeout(() => {
      foxMascot.style.opacity = "1";
    }, 350);
  });
};

// DateTime Module
const initDateTime = () => {
  const datetimeElement = document.querySelector(".datetime");
  if (!datetimeElement) {
    console.error("Element .datetime not found!");
    return;
  }

  const updateDateTime = () => {
    const now = new Date();
    const dateStr = now.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
    datetimeElement.textContent = dateStr;
  };

  updateDateTime();
  setInterval(updateDateTime, 1000);
};

// Chatbot Module
const initChatbot = () => {
  const elements = {
    toggle: document.getElementById("chatbot-toggle"),
    window: document.getElementById("chatbot-window"),
    close: document.getElementById("chatbot-close"),
    clear: document.getElementById("chatbot-clear"),
    messages: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-messages"),
    input: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-input input"),
    sendButton: document
      .getElementById("chatbot-window")
      ?.querySelector(".chatbot-input button"),
  };

  if (!Object.values(elements).every(Boolean)) {
    console.error("One or more chatbot elements not found!");
    return;
  }

  // Historique de la conversation
  let chatHistory = [
    { role: 'assistant', content: 'Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
  ];

  const handleOpenChat = () => {
    elements.window.classList.remove("hidden", "closing");
    elements.window.classList.add("opening");
    elements.toggle.classList.add("hidden");

    elements.window.addEventListener(
      "animationend",
      () => {
        elements.window.classList.remove("opening");
      },
      { once: true }
    );
  };

  const handleCloseChat = () => {
    elements.window.classList.remove("opening");
    elements.window.classList.add("closing");

    elements.window.addEventListener(
      "animationend",
      () => {
        elements.window.classList.add("hidden");
        elements.window.classList.remove("closing");
        elements.toggle.classList.remove("hidden");
      },
      { once: true }
    );
  };

  const handleClearChat = () => {
    // Garder uniquement le message d'accueil dans l'affichage
    elements.messages.innerHTML = '<div class="message bot">Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.</div>';
    // Réinitialiser l'historique
    chatHistory = [
      { role: 'assistant', content: 'Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
    ];
  };

  // URL de votre fonction Cloud
  const CLOUD_FUNCTION_URL = 'https://cloudfunc-llm-994261001875.europe-west9.run.app';

  // Fonction pour ajouter un message au chat (affichage et historique)
  const addMessage = (content, isBot = false) => {
    const role = isBot ? 'bot' : 'user'; // Correction: utiliser 'bot' au lieu de 'assistant'
    const messageDiv = createElement('div', `message ${role}`);
    messageDiv.textContent = content;
    elements.messages.appendChild(messageDiv);
    // Faire défiler vers le bas pour voir le nouveau message
    elements.messages.scrollTop = elements.messages.scrollHeight;

    // Ajouter à l'historique
    chatHistory.push({ role, content });
  }

  // Fonction pour envoyer un message au LLM via la fonction Cloud
  const sendMessageToLLM = async (message) => {
    // Ajouter un indicateur de chargement
    const loadingDiv = createElement('div', 'message bot loading');
    loadingDiv.textContent = 'Je réfléchis ...';
    elements.messages.appendChild(loadingDiv);
    elements.messages.scrollTop = elements.messages.scrollHeight; // Scroll after adding loading

    // Préparer l'historique pour l'envoi
    const historyToSend = chatHistory.slice(); // Copie pour ne pas modifier l'original ici

    try {
      const response = await fetch(CLOUD_FUNCTION_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        // Envoyer le dernier message et l'historique
        body: JSON.stringify({ message: message, history: historyToSend })
      });

      // Supprimer l'indicateur de chargement dès que la réponse arrive
      if (elements.messages.contains(loadingDiv)) {
        elements.messages.removeChild(loadingDiv);
      }

      if (!response.ok) {
        // Gérer les erreurs HTTP (ex: 404, 500)
        let errorMsg = `Erreur HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMsg = errorData.error || errorMsg;
        } catch (e) { /* Ignorer si le corps n'est pas JSON */ }
        addMessage(`Désolé, une erreur serveur est survenue (${response.status}). Veuillez réessayer plus tard.`, true);
        console.error('Erreur serveur:', errorMsg);
        return; // Arrêter le traitement ici
      }

      const data = await response.json();

      if (data.response) {
        // Ajouter la réponse du LLM (addMessage l'ajoute aussi à l'historique)
        addMessage(data.response, true);
      } else {
        // Gérer le cas où la réponse est OK mais sans contenu attendu
        addMessage("Désolé, je n'ai pas pu obtenir de réponse valide. Veuillez réessayer.", true);
        console.error('Erreur: Réponse invalide ou manquante', data);
      }

    } catch (error) {
      // Supprimer l'indicateur de chargement si toujours présent (ex: erreur réseau)
      if (elements.messages.contains(loadingDiv)) {
          elements.messages.removeChild(loadingDiv);
      }
      // Afficher un message d'erreur générique pour les problèmes de connexion/fetch
      addMessage("Désolé, une erreur de communication s'est produite. Veuillez vérifier votre connexion et réessayer.", true);
      console.error('Erreur de communication:', error);
    }
  }

  // Gérer l'envoi de message (bouton ou touche Entrée)
  const handleSendMessage = () => {
    const message = elements.input.value.trim();
    if (message) {
      // Ajouter le message de l'utilisateur (addMessage l'ajoute aussi à l'historique)
      addMessage(message);

      // Envoyer au LLM (avec l'historique mis à jour)
      sendMessageToLLM(message);

      // Effacer le champ de saisie
      elements.input.value = '';
    }
  }

  // Event Listeners
  elements.toggle.addEventListener("click", handleOpenChat);
  elements.close.addEventListener("click", handleCloseChat);
  elements.clear.addEventListener("click", handleClearChat);
  elements.sendButton.addEventListener("click", handleSendMessage);
  elements.input.addEventListener("keypress", (event) => {
    if (event.key === "Enter") handleSendMessage();
  });

  // Afficher le message d'accueil initial (déjà dans l'historique)
  elements.messages.innerHTML = `<div class="message bot">${chatHistory[0].content}</div>`;
};

// Sidebar Toggle Module
const initSidebarToggle = () => {
  const sidebar = document.querySelector(".sidebar-definitions");
  const toggleButton = document.querySelector(".sidebar-toggle");

  if (!sidebar || !toggleButton) {
    console.error("Sidebar or toggle button not found!");
    return;
  }

  // Fonction pour créer l'effet machine à écrire
  const typewriterEffect = (element, text, speed = 30) => {
    element.textContent = "";
    let i = 0;
    
    const typeChar = () => {
      if (i < text.length) {
        element.textContent += text.charAt(i);
        i++;
        setTimeout(typeChar, speed);
      }
    };
    
    typeChar();
  };

  // Sauvegarder le contenu original des éléments
  const originalContent = new Map();
  const definitionItems = sidebar.querySelectorAll('.definitions-list li');
  const sidebarTitle = sidebar.querySelector('h2');
  
  // Sauvegarder les contenus originaux
  if (sidebarTitle) {
    originalContent.set(sidebarTitle, sidebarTitle.textContent);
  }
  
  definitionItems.forEach((item, index) => {
    const strongElement = item.querySelector('strong');
    const textContent = item.childNodes[item.childNodes.length - 1].textContent;
    
    originalContent.set(`strong-${index}`, strongElement ? strongElement.textContent : '');
    originalContent.set(`text-${index}`, textContent);
  });

  // Fonction pour démarrer l'animation de la sidebar
  const animateSidebarContent = () => {
    // Effacer tout le contenu d'abord
    if (sidebarTitle) {
      sidebarTitle.textContent = "";
    }
    
    definitionItems.forEach(item => {
      const strongElement = item.querySelector('strong');
      if (strongElement) {
        strongElement.textContent = "";
      }
      // Effacer le texte après le strong
      const textNodes = Array.from(item.childNodes).filter(node => node.nodeType === Node.TEXT_NODE);
      textNodes.forEach(node => node.textContent = "");
    });

    // Démarrer l'animation du titre avec un délai
    setTimeout(() => {
      if (sidebarTitle) {
        typewriterEffect(sidebarTitle, originalContent.get(sidebarTitle), 50);
      }
    }, 200);

    // Animer chaque définition avec des délais progressifs
    definitionItems.forEach((item, index) => {
      const strongElement = item.querySelector('strong');
      const delay = 500 + (index * 800); // Délai progressif entre chaque élément
      
      setTimeout(() => {
        if (strongElement) {
          typewriterEffect(strongElement, originalContent.get(`strong-${index}`), 40);
          
          // Animer le texte descriptif après le titre
          setTimeout(() => {
            const textNodes = Array.from(item.childNodes).filter(node => node.nodeType === Node.TEXT_NODE);
            if (textNodes.length > 0) {
              const textNode = textNodes[textNodes.length - 1];
              const originalText = originalContent.get(`text-${index}`);
              textNode.textContent = "";
              
              let i = 0;
              const typeText = () => {
                if (i < originalText.length) {
                  textNode.textContent += originalText.charAt(i);
                  i++;
                  setTimeout(typeText, 25);
                }
              };
              typeText();
            }
          }, strongElement.textContent.length * 40 + 100);
        }
      }, delay);
    });
  };

  // Ouvre/ferme le menu au clic sur le bouton
  toggleButton.addEventListener("click", (e) => {
    e.stopPropagation();
    const isOpen = sidebar.classList.toggle("open");
    toggleButton.setAttribute("aria-expanded", isOpen);
    toggleButton.classList.toggle("open", isOpen);
    
    if (isOpen) {
      // Démarrer l'animation machine à écrire quand la sidebar s'ouvre
      animateSidebarContent();
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
  });

  // Ferme le menu si clic en dehors du sidebar ET du bouton
  function handleClickOutside(e) {
    if (!sidebar.contains(e.target) && !toggleButton.contains(e.target)) {
      sidebar.classList.remove("open");
      toggleButton.classList.remove("open");
      toggleButton.setAttribute("aria-expanded", "false");
      document.removeEventListener("mousedown", handleClickOutside);
    }
  }
};

// Accessibility Module
const initAccessibility = () => {
  document.querySelectorAll("a, button, input, textarea").forEach((el) => {
    el.setAttribute("tabindex", "0");
  });
};

// === Theme Switcher ===
const initThemeSwitcher = () => {
  const themeToggle = document.getElementById('theme-toggle');
  const themeIcon = themeToggle?.querySelector('.theme-icon');
  const body = document.body;

  // Applique le thème sauvegardé
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'light') {
    body.classList.add('light-theme');
    if (themeIcon) themeIcon.textContent = '☀️';
  } else {
    body.classList.remove('light-theme');
    if (themeIcon) themeIcon.textContent = '🌙';
  }

  themeToggle?.addEventListener('click', () => {
    body.classList.toggle('light-theme');
    const isLight = body.classList.contains('light-theme');
    if (themeIcon) themeIcon.textContent = isLight ? '☀️' : '🌙';
    localStorage.setItem('theme', isLight ? 'light' : 'dark');
    console.log('Thème actuel :', isLight ? 'light' : 'dark');
  });

  // Ajoute une transition douce sur le body
  body.style.transition = 'background 0.4s, color 0.4s';
};

// Projects Expansion Module
const initProjectsExpansion = () => {
  const projectSummaries = document.querySelectorAll('.project-summary');
  
projectSummaries.forEach(summary => {
  const toggleProject = () => {
     const projectId = summary.getAttribute('data-project');
     const details = document.getElementById(`project-${projectId}`);
     
     // Check if this project is already active
     const isActive = summary.classList.contains('active');
     
     // Close all project details first
     document.querySelectorAll('.project-summary').forEach(s => s.classList.remove('active'));
     document.querySelectorAll('.project-details').forEach(d => d.classList.remove('active'));
     
     // Then open the clicked one if it wasn't already open
     if (!isActive) {
       summary.classList.add('active');
       details.classList.add('active');
      summary.setAttribute('aria-expanded', 'true');
    } else {
      summary.setAttribute('aria-expanded', 'false');
     }
  };
  
  // Add click event
  summary.addEventListener('click', toggleProject);
  
  // Add keyboard support
  summary.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleProject();
    }
  });
 });
};

// Initialize all modules
document.addEventListener("DOMContentLoaded", () => {
  initNavigation();
  initSectionAnimations();
  initSkillsAnimation();
  initContactForm();
  initMascotInteraction();
  initDateTime();
  initChatbot();
  initAccessibility();
  initSidebarToggle();
  initThemeSwitcher();
  initProjectsExpansion();
});
