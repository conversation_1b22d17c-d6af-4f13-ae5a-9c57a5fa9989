<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RedFox – Portfolio IA Générative</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <nav class="navbar">
      <button
        class="sidebar-toggle"
        aria-label="Ouvrir/fermer le menu"
        aria-controls="sidebar-definitions"
        aria-expanded="false"
      >
        <span class="hamburger"><span></span><span></span><span></span></span>
      </button>
      <ul>
        <li><a href="#accueil">Accueil</a></li>
        <li><a href="#a-propos">À propos</a></li>
        <li><a href="#competences">Compétences</a></li>
        <li><a href="#projets">Projets</a></li>
        <li><a href="#certifications">Certifications</a></li>
        <li><a href="#contact">Contact</a></li>
      </ul>
      <button id="theme-toggle" class="theme-toggle" aria-label="Changer de thème" title="Changer de thème">
        <span class="theme-icon">🌙</span>
      </button>
    </nav>
    <aside class="sidebar-definitions" id="sidebar-definitions">
      <h2>Définitions</h2>
      <ul class="definitions-list">
        <li><strong>IA Générative</strong>Technologie d'intelligence artificielle capable de créer du contenu original (texte, images, sons) en s'inspirant de données existantes. Exemples : ChatGPT, DALL-E, Midjourney.</li>
        <li><strong>Machine Learning</strong>Branche de l'IA permettant aux ordinateurs d'apprendre à partir de données sans être explicitement programmés. Fondement des systèmes prédictifs et de reconnaissance de motifs.</li>
        <li><strong>Développeur Fullstack</strong>Professionnel maîtrisant à la fois le développement frontend (interface utilisateur) et backend (serveur, base de données), capable de créer des applications web complètes.</li>
        <li><strong>Intégrateur Cloud</strong>Expert qui implémente, configure et optimise les solutions cloud pour les entreprises, assurant la migration et l'interconnexion des services.</li>
        <li><strong>Pixel Art</strong>Style graphique numérique où les images sont créées au niveau du pixel, souvent avec une palette de couleurs limitée, évoquant l'esthétique des jeux vidéo rétro.</li>
        <li><strong>Azure</strong>Plateforme cloud de Microsoft offrant des services d'hébergement, de calcul, d'IA et d'analyse de données pour les entreprises et développeurs.</li>
        <li><strong>AZ-900</strong>Certification Microsoft Azure Fundamentals qui valide les connaissances de base sur les services cloud, les modèles de tarification et la conformité dans Azure.</li>
      </ul>
    </aside>

    <section id="accueil" class="section accueil">
      <div class="accueil-content">
        <div class="mascotte-container">
          <!-- Pixel Art Fox Mascot SVG -->
          <img
            src="images/fox_transparent.png"
            alt="Logo RedFox"
            class="logo-img"
            style="max-width: 120px; margin-top: 12px"
          />
        </div>
        <h1>RedFox</h1>
        <p class="slogan">
          Comprendre, développer et travailler avec l'IA : dans tous vos
          métiers.
        </p>
        <br />
      </div>
      <!-- Mini carte d'identité -->
      <div class="mini-id-card">
        <span class="flag-fr" aria-label="Français"></span>
        <div class="id-info">
          <span class="id-name">Jérémy M.</span>
          <span class="id-title"
            >Développeur polymorphe <br />Consultant-Expert en IA Générative
            <br />Intégrateur Cloud (spé. Azure)</span
          >
          <span class="id-city">Région Grand Ouest, France</span>
        </div>
      </div>
    </section>

    <section id="a-propos" class="section a-propos">
      <h1>Bienvenue ici,</h1>
      <div class="about-content">
        <span class="emoji-fox"></span>
        <div>
          <p>
            Expert en intelligence artificielle générative et développeur
            fullstack, je me spécialise dans la conception de solutions
            sur-mesure. <br />Du brouillon à l'intégration, tout y est. Depuis
            fin 2024 j'accompagne une grande diversité de clients, privés,
            collectivités et public dans leur quête du train de l'IA. <br /><i
              >Oui, cette dernière a sa place dans tous les métiers et non, elle
              ne vous remplacera pas pour autant !</i
            >
          </p>
        </div>
      </div>
    </section>

    <section id="competences" class="section competences">
      <h2>Compétences</h2>
      <div class="skills-list">
        <div class="skill">
          <span class="skill-name">IA Générative</span>
          <div class="skill-bar" data-level="95"></div>
        </div>
        <div class="skill">
          <span class="skill-name">Machine Learning</span>
          <div class="skill-bar" data-level="90"></div>
        </div>
        <div class="skill">
          <span class="skill-name">Python</span>
          <div class="skill-bar" data-level="85"></div>
        </div>
        <div class="skill">
          <span class="skill-name">JavaScript</span>
          <div class="skill-bar" data-level="80"></div>
        </div>
        <div class="skill">
          <span class="skill-name">Pixel Art</span>
          <div class="skill-bar" data-level="75"></div>
        </div>
      </div>
    </section>

    <section id="projets" class="section projets">
      <h2>Projets</h2>

      <!-- Filtres des projets -->
      <div class="projects-filters" role="region" aria-label="Filtres des projets">
        <div class="filter-search">
          <label for="project-search" class="sr-only">Rechercher un projet</label>
          <input
            type="text"
            id="project-search"
            class="search-input"
            placeholder="Rechercher un projet..."
            aria-describedby="search-help"
          />
          <span id="search-help" class="sr-only">Tapez pour filtrer les projets par nom ou description</span>
        </div>

        <div class="filter-tags">
          <span class="filter-label">Technologies :</span>
          <div class="tag-filters" role="group" aria-label="Filtrer par technologies">
            <button class="tag-filter active" data-tag="all" aria-pressed="true">Tous</button>
            <button class="tag-filter" data-tag="Python" aria-pressed="false">Python</button>
            <button class="tag-filter" data-tag="IA Générative" aria-pressed="false">IA Générative</button>
            <button class="tag-filter" data-tag="JavaScript" aria-pressed="false">JavaScript</button>
            <button class="tag-filter" data-tag="Pixel Art" aria-pressed="false">Pixel Art</button>
            <button class="tag-filter" data-tag="Web Audio API" aria-pressed="false">Web Audio</button>
          </div>
        </div>

        <div class="filter-results" aria-live="polite" aria-atomic="true">
          <span id="results-count">3 projets affichés</span>
        </div>
      </div>

      <div class="projects-list">
        <div class="project-item" data-tags="Python,IA Générative,Pixel Art" data-search-text="foxgen générateur images ia pixel art faune sauvage">
          <div class="project-summary" data-project="foxgen" role="button" aria-expanded="false" aria-controls="project-foxgen" tabindex="0">
            <div class="project-pixelart" role="img" aria-label="Pixel art icon for FoxGen project"></div>
            <h3>FoxGen</h3>
            <span class="expand-arrow">▶</span>
          </div>
          <div class="project-details" id="project-foxgen">
            <div class="project-image">
  <img src="images/projects/foxgen.jpg" alt="Screenshot of FoxGen project" />
</div>
            <div class="project-info">
              <h3>FoxGen</h3>
              <p class="project-description"><i>Générateur d'images IA inspiré du pixel art et de la faune sauvage.</i></p>
              <div class="project-meta">
                <span class="project-date"><span class="date-label">Début:</span> <span class="date-value">01/2024</span></span>
                <span class="project-date"><span class="date-label">Fin:</span> <span class="date-value">03/2024</span></span>
              </div>
              <a href="pages/projets.html" class="more-info-btn">+ d'infos</a>
            </div>
          </div>
        </div>

        <div class="project-item" data-tags="JavaScript,IA Générative" data-search-text="text2quest aventures textuelles interactives ia générative jeu">
          <div class="project-summary" data-project="text2quest" role="button" aria-expanded="false" aria-controls="project-text2quest" tabindex="0">
            <div class="project-pixelart" role="img" aria-label="Pixel art icon for Text2Quest project"></div>
            <h3>Text2Quest</h3>
            <span class="expand-arrow">▶</span>
          </div>
          <div class="project-details" id="project-text2quest">
            <div class="project-image"></div>
            <div class="project-info">
              <h3>Text2Quest</h3>
              <p class="project-description"><i>Création d'aventures textuelles interactives grâce à l'IA générative.</i></p>
              <div class="project-meta">
                <span class="project-date"><span class="date-label">Début:</span> <span class="date-value">02/2024</span></span>
                <span class="project-date"><span class="date-label">Fin:</span> <span class="date-value">05/2024</span></span>
              </div>
              <a href="pages/projets.html" class="more-info-btn">+ d'infos</a>
            </div>
          </div>
        </div>

        <div class="project-item" data-tags="JavaScript,Web Audio API,IA Générative" data-search-text="retrosynth synthétiseur sons rétro ia jeux vidéo pixel art audio">
          <div class="project-summary" data-project="retrosynth" role="button" aria-expanded="false" aria-controls="project-retrosynth" tabindex="0">
            <div class="project-pixelart" role="img" aria-label="Pixel art icon for RetroSynth project"></div>
            <h3>RetroSynth</h3>
            <span class="expand-arrow">▶</span>
          </div>
          <div class="project-details" id="project-retrosynth">
            <div class="project-image"></div>
            <div class="project-info">
              <h3>RetroSynth</h3>
              <p class="project-description"><i>Synthétiseur de sons rétro piloté par IA pour jeux vidéo pixel art.</i></p>
              <div class="project-meta">
                <span class="project-date"><span class="date-label">Début:</span> <span class="date-value">04/2024</span></span>
                <span class="project-date"><span class="date-label">Fin:</span> <span class="date-value">En cours</span></span>
              </div>
              <a href="pages/projets.html" class="more-info-btn">+ d'infos</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="certifications" class="section certifications">
      <h2>Certifications</h2>
      <div class="certifications-list">
        <div class="certification-item">
          <img src="images/microsoft-certified-associate-badge.png" alt="Badge Microsoft Certified Associate" class="certification-badge">
          <p><strong>AI-102 / AI Engineer Associate</strong> Microsoft - 2025</p>
        </div>
        <div class="certification-item">
          <img src="images/microsoft-certified-fundamentals-badge.png" alt="Badge Microsoft Certified Fundamentals" class="certification-badge">
          <p><strong>AZ-900 / Azure Fundamentals</strong> Microsoft - 2024</p>
        </div>
      </div>
    </section>

    <section id="contact" class="section contact">
      <h2>Contact</h2>
      <form class="contact-form" autocomplete="off">
        <label for="name">Nom</label>
        <input type="text" id="name" name="name" required />
        <label for="email">Email</label>
        <input type="email" id="email" name="email" required />
        <label for="message">Message</label>
        <textarea id="message" name="message" rows="3" required></textarea>
        <button type="submit">Envoyer</button>
      </form>
      <div class="contact-links">
        <a href="mailto:<EMAIL>" class="contact-link">Email</a>
        <a href="#" class="contact-link">GitHub</a>
        <a href="#" class="contact-link">LinkedIn</a>
      </div>
      <div class="mascotte-contact">
        <!-- Mascotte fox will react here on form submit -->
        <img
          src="images/fox_transparent.png"
          alt="Logo RedFox"
          class="logo-img"
          style="max-width: 80px"
        />
      </div>
    </section>

    <script src="script.js" defer></script>

    <!-- Chatbot Button/Window -->
    <div class="chatbot-container">
      <button id="chatbot-toggle" class="chatbot-button">
        <!-- Add a chat icon or small mascot image later -->
        <span>💬</span>
      </button>
      <div id="chatbot-window" class="chatbot-window hidden">
        <div class="chatbot-header">
          <span>Interrogez-moi !</span>
          <div>
            <!-- Conteneur pour les boutons -->
            <button
              id="chatbot-clear"
              class="chatbot-clear-button"
              title="Effacer le chat"
            >
              🗑️
            </button>
            <button
              id="chatbot-close"
              class="chatbot-close-button"
              title="Fermer"
            >
              &times;
            </button>
          </div>
        </div>
        <div class="chatbot-messages">
          <!-- Placeholder messages -->
          <div class="message bot">
            Bonjour, je suis à votre disposition pour répondre à vos questions
            sur RedFox.
          </div>
        </div>
        <div class="chatbot-input">
          <input type="text" placeholder="Votre message..." />
          <button>Envoyer</button>
        </div>
      </div>
    </div>

    <footer class="footer">
      <p>&copy; 2025 RedFox. Tous droits réservés.</p>
      <p class="datetime"></p>
    </footer>
  </body>
</html>
